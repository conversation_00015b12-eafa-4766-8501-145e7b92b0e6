# ThinkBoard Backend Environment Variables
# Copy this file to .env and fill in your actual values

# ===========================================
# MONGODB ATLAS CONFIGURATION (REQUIRED)
# ===========================================
# Get this from MongoDB Atlas:
# 1. Go to https://cloud.mongodb.com/
# 2. Create cluster (M0 free tier available)
# 3. Create database user
# 4. Configure network access
# 5. Get connection string from "Connect" button
MONGO_URI=mongodb+srv://username:<EMAIL>/thinkboard?retryWrites=true&w=majority

# ===========================================
# UPSTASH REDIS CONFIGURATION (OPTIONAL)
# ===========================================
# Get these from Upstash Console:
# 1. Go to https://console.upstash.com/
# 2. Create Redis database (free tier available)
# 3. Copy REST URL and Token from dashboard
UPSTASH_REDIS_REST_URL=https://your-endpoint.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-token-here

# ===========================================
# SERVER CONFIGURATION
# ===========================================
NODE_ENV=development
PORT=5030

# ===========================================
# CORS CONFIGURATION (PRODUCTION)
# ===========================================
# Set this to your frontend domain in production
# For development, leave commented out
# FRONTEND_URL=https://your-frontend-domain.com

# ===========================================
# SECURITY (OPTIONAL)
# ===========================================
# JWT secret for future authentication features
# JWT_SECRET=your-super-secret-jwt-key

# API rate limiting (requests per minute)
# RATE_LIMIT_REQUESTS=100
# RATE_LIMIT_WINDOW=60
