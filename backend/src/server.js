import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import { connectDB } from "./config/db.js";
import notesRoutes from "./routes/notesRoutes.js";

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5030;

// CORS configuration - cloud-ready
const corsOptions = {
  origin: process.env.NODE_ENV === "production"
    ? [process.env.FRONTEND_URL].filter(Boolean) // Production: use environment variable
    : ["http://localhost:3000", "http://localhost:5173", "http://localhost:5174"], // Development: local URLs
  credentials: true,
  optionsSuccessStatus: 200 // Support legacy browsers
};

app.use(cors(corsOptions));

// Parse JSON bodies
app.use(express.json());

// Health check endpoint with cloud service status
app.get("/api/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    message: "ThinkBoard API is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
    services: {
      database: "MongoDB Atlas",
      rateLimit: process.env.UPSTASH_REDIS_REST_URL ? "Upstash Redis" : "Disabled",
      cors: process.env.NODE_ENV === "production" ? "Production" : "Development"
    },
    version: "1.0.0"
  });
});

// API routes
app.use("/api/notes", notesRoutes);

// Start server (MongoDB connection is optional for now)
app.listen(PORT, () => {
  console.log(`🚀 Server started on PORT: ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📝 API Base URL: http://localhost:${PORT}/api`);

  // Try to connect to MongoDB (non-blocking)
  connectDB().catch((error) => {
    console.warn("⚠️  MongoDB connection failed, but server is still running:", error.message);
    console.warn("⚠️  Database features will be unavailable until MongoDB is connected");
  });
});
