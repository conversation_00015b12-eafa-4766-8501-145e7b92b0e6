import mongoose from "mongoose";

export const connectDB = async () => {
  try {
    // Validate MongoDB URI
    if (!process.env.MONGO_URI) {
      throw new Error('MONGO_URI environment variable is not defined');
    }

    // Connect to MongoDB with cloud-optimized options
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      // Cloud database optimized settings
      serverSelectionTimeoutMS: 10000, // 10 seconds for cloud connections
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      maxPoolSize: 10, // Maintain up to 10 socket connections
      minPoolSize: 2, // Maintain minimum 2 socket connections
      maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
      bufferMaxEntries: 0, // Disable mongoose buffering
      bufferCommands: false, // Disable mongoose buffering
      retryWrites: true, // Enable retryable writes for MongoDB Atlas
    });

    console.log(`✅ MONGODB ATLAS CONNECTED SUCCESSFULLY!`);
    console.log(`📍 Connected to: ${conn.connection.host}`);
    console.log(`🗄️  Database: ${conn.connection.name}`);
    console.log(`🌐 Connection type: ${process.env.MONGO_URI.includes('mongodb+srv') ? 'Cloud (Atlas)' : 'Local'}`);

  } catch (error) {
    console.error("❌ Error connecting to MONGODB:", error.message);

    // Provide helpful error messages for common cloud connection issues
    if (error.message.includes('authentication failed')) {
      console.error("💡 Check your MongoDB Atlas username and password");
    } else if (error.message.includes('connection timed out')) {
      console.error("💡 Check your network access settings in MongoDB Atlas");
    } else if (error.message.includes('ENOTFOUND')) {
      console.error("💡 Check your MongoDB connection string format");
    }

    throw error; // Don't exit process, let caller handle it
  }
};

// Handle connection events
mongoose.connection.on('disconnected', () => {
  console.log('⚠️  MongoDB disconnected');
});

mongoose.connection.on('error', (err) => {
  console.error('❌ MongoDB connection error:', err);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    console.log('🔌 MongoDB connection closed through app termination');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during MongoDB disconnection:', error);
    process.exit(1);
  }
});
