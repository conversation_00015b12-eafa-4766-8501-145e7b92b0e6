import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";
import dotenv from "dotenv";

dotenv.config();

let ratelimit = null;
let redis = null;

// Initialize Redis connection and rate limiter
const initializeRedis = async () => {
  // Check if Redis credentials are provided
  if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
    try {
      // Create Redis client
      redis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL,
        token: process.env.UPSTASH_REDIS_REST_TOKEN,
      });

      // Test Redis connection
      await redis.ping();

      // Create rate limiter with cloud-optimized settings
      ratelimit = new Ratelimit({
        redis: redis,
        limiter: Ratelimit.slidingWindow(100, "60 s"), // 100 requests per minute
        analytics: true,
        prefix: "thinkboard_ratelimit", // Custom prefix for this app
      });

      console.log("✅ Upstash Redis connected successfully");
      console.log("✅ Rate limiting enabled (100 requests/minute)");

    } catch (error) {
      console.warn("⚠️  Failed to connect to Upstash Redis:", error.message);
      console.warn("⚠️  Rate limiting will be disabled");

      // Provide helpful error messages
      if (error.message.includes('Unauthorized')) {
        console.warn("💡 Check your UPSTASH_REDIS_REST_TOKEN");
      } else if (error.message.includes('ENOTFOUND')) {
        console.warn("💡 Check your UPSTASH_REDIS_REST_URL format");
      }

      ratelimit = null;
      redis = null;
    }
  } else {
    console.log("ℹ️  Rate limiting disabled - Upstash Redis credentials not provided");
    console.log("ℹ️  To enable rate limiting:");
    console.log("   1. Create account at https://console.upstash.com/");
    console.log("   2. Create Redis database");
    console.log("   3. Add UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN to .env");
  }
};

// Initialize Redis on module load
initializeRedis();

// Export rate limiter and Redis client
export default ratelimit;
export { redis };
