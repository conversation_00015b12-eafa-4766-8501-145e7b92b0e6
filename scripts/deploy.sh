#!/bin/bash

# ThinkBoard Deployment Script
# This script helps deploy ThinkBoard to various cloud platforms

set -e  # Exit on any error

echo "🚀 ThinkBoard Deployment Script"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if required tools are installed
check_dependencies() {
    print_info "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16+ and try again."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    print_status "Dependencies check passed"
}

# Install dependencies
install_dependencies() {
    print_info "Installing dependencies..."
    
    # Install root dependencies
    npm install
    
    # Install backend dependencies
    cd backend
    npm install
    cd ..
    
    # Install frontend dependencies
    cd frontend
    npm install
    cd ..
    
    print_status "Dependencies installed successfully"
}

# Build frontend
build_frontend() {
    print_info "Building frontend..."
    
    cd frontend
    npm run build
    cd ..
    
    print_status "Frontend built successfully"
}

# Check environment variables
check_env_vars() {
    print_info "Checking environment variables..."
    
    # Check backend .env
    if [ ! -f "backend/.env" ]; then
        print_warning "Backend .env file not found"
        print_info "Copy backend/.env.example to backend/.env and configure your cloud credentials"
        return 1
    fi
    
    # Check frontend .env
    if [ ! -f "frontend/.env" ]; then
        print_warning "Frontend .env file not found"
        print_info "Copy frontend/.env.example to frontend/.env and configure your API URL"
        return 1
    fi
    
    print_status "Environment files found"
}

# Test cloud connections
test_connections() {
    print_info "Testing cloud service connections..."
    
    cd backend
    
    # Test MongoDB connection
    node -e "
        require('dotenv').config();
        const mongoose = require('mongoose');
        
        if (!process.env.MONGO_URI) {
            console.log('❌ MONGO_URI not configured');
            process.exit(1);
        }
        
        mongoose.connect(process.env.MONGO_URI)
            .then(() => {
                console.log('✅ MongoDB Atlas connection successful');
                process.exit(0);
            })
            .catch(err => {
                console.log('❌ MongoDB Atlas connection failed:', err.message);
                process.exit(1);
            });
    " || print_warning "MongoDB connection test failed"
    
    cd ..
    
    print_status "Connection tests completed"
}

# Deploy to Vercel (Frontend)
deploy_vercel() {
    print_info "Deploying frontend to Vercel..."
    
    if ! command -v vercel &> /dev/null; then
        print_warning "Vercel CLI not installed. Installing..."
        npm install -g vercel
    fi
    
    cd frontend
    vercel --prod
    cd ..
    
    print_status "Frontend deployed to Vercel"
}

# Deploy to Railway (Backend)
deploy_railway() {
    print_info "Deploying backend to Railway..."
    
    if ! command -v railway &> /dev/null; then
        print_warning "Railway CLI not installed. Please install it manually:"
        print_info "npm install -g @railway/cli"
        return 1
    fi
    
    cd backend
    railway deploy
    cd ..
    
    print_status "Backend deployed to Railway"
}

# Main deployment function
deploy() {
    local platform=$1
    
    case $platform in
        "vercel")
            deploy_vercel
            ;;
        "railway")
            deploy_railway
            ;;
        "full")
            deploy_vercel
            deploy_railway
            ;;
        *)
            print_error "Unknown platform: $platform"
            print_info "Available platforms: vercel, railway, full"
            exit 1
            ;;
    esac
}

# Show help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Install dependencies and build project"
    echo "  build     - Build frontend for production"
    echo "  test      - Test cloud service connections"
    echo "  deploy    - Deploy to cloud platforms"
    echo "  help      - Show this help message"
    echo ""
    echo "Deploy options:"
    echo "  ./deploy.sh deploy vercel   - Deploy frontend to Vercel"
    echo "  ./deploy.sh deploy railway  - Deploy backend to Railway"
    echo "  ./deploy.sh deploy full     - Deploy to both platforms"
}

# Main script logic
case $1 in
    "setup")
        check_dependencies
        install_dependencies
        build_frontend
        print_status "Setup completed successfully!"
        print_info "Next steps:"
        print_info "1. Configure your .env files with cloud credentials"
        print_info "2. Run './deploy.sh test' to test connections"
        print_info "3. Run './deploy.sh deploy full' to deploy"
        ;;
    "build")
        build_frontend
        ;;
    "test")
        check_env_vars
        test_connections
        ;;
    "deploy")
        check_dependencies
        check_env_vars || exit 1
        build_frontend
        deploy $2
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        print_info "ThinkBoard Deployment Script"
        print_info "Run './deploy.sh help' for usage information"
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
