# ThinkBoard - MERN Note-Taking App

A modern, responsive note-taking application built with the MERN stack (MongoDB, Express.js, React, Node.js). ThinkBoard allows users to create, edit, organize, and manage their notes with a beautiful, intuitive interface.

## 🚀 Features

### Core Functionality
- ✅ **Create, Read, Update, Delete (CRUD) Notes**
- ✅ **Pin Important Notes** - Keep important notes at the top
- ✅ **Categorize Notes** - Organize notes by categories (Work, Personal, Ideas, etc.)
- ✅ **Tag System** - Add multiple tags to notes for better organization
- ✅ **Search & Filter** - Search notes by title, content, or tags
- ✅ **Real-time Updates** - Instant feedback on all operations

### User Experience
- ✅ **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- ✅ **Dark/Light Theme** - Toggle between themes with persistent preference
- ✅ **Smooth Animations** - Polished UI with fade-in and hover effects
- ✅ **Toast Notifications** - User-friendly feedback for all actions
- ✅ **Loading States** - Clear loading indicators for better UX

### Technical Features
- ✅ **RESTful API** - Well-structured backend API
- ✅ **Error Handling** - Comprehensive error handling and validation
- ✅ **Rate Limiting** - API protection with configurable rate limits
- ✅ **Offline Fallback** - App works with mock data when API is unavailable
- ✅ **Security** - Input sanitization and CORS protection

## 🛠️ Tech Stack

### Frontend
- **React 18** - Modern React with hooks
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **DaisyUI** - Beautiful component library
- **Axios** - HTTP client for API calls
- **React Hot Toast** - Elegant toast notifications
- **Lucide React** - Beautiful icons

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **MongoDB** - NoSQL database
- **Mongoose** - MongoDB object modeling
- **CORS** - Cross-origin resource sharing
- **Dotenv** - Environment variable management

## 📦 Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager
- **Cloud Services (Recommended)**:
  - MongoDB Atlas account (free tier available)
  - Upstash Redis account (free tier available)
- **OR Local Development**:
  - MongoDB (local installation)

### 🚀 Quick Start (Cloud Setup)

#### 1. Clone the Repository
```bash
git clone <repository-url>
cd MERN-BIDBEES-THINKBOARD
```

#### 2. Automated Setup
```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Install dependencies and build project
./scripts/deploy.sh setup
```

#### 3. Configure Cloud Services
```bash
# Copy environment templates
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Edit backend/.env with your cloud credentials:
# - MongoDB Atlas connection string
# - Upstash Redis credentials (optional)

# Edit frontend/.env with your API URL
```

#### 4. Test Cloud Connections
```bash
# Test your cloud service connections
./scripts/deploy.sh test
```

#### 5. Start Development Servers
```bash
# Start both frontend and backend
npm run dev

# Or start individually:
# Backend: npm run dev:backend
# Frontend: npm run dev:frontend
```

### 🔧 Manual Setup (Alternative)

#### Backend Setup
```bash
cd backend
npm install

# Create environment file
cp .env.example .env
# Edit .env with your cloud credentials

# Start the backend server
npm run dev
```

#### Frontend Setup
```bash
cd frontend
npm install

# Create environment file
cp .env.example .env
# Edit .env with your API URL

# Start the frontend development server
npm run dev
```

### 🌐 Cloud Configuration

#### MongoDB Atlas (.env)
```env
# Get from MongoDB Atlas dashboard
MONGO_URI=mongodb+srv://username:<EMAIL>/thinkboard?retryWrites=true&w=majority
```

#### Upstash Redis (.env) - Optional
```env
# Get from Upstash Console
UPSTASH_REDIS_REST_URL=https://your-endpoint.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-token-here
```

#### Frontend API (.env)
```env
# Development
VITE_API_URL=http://localhost:5030/api

# Production
VITE_API_URL=https://your-backend-domain.com/api
```

> 📖 **Need help with cloud setup?** See [CLOUD_SETUP.md](./CLOUD_SETUP.md) for detailed instructions.

## 🚀 Running the Application

### Development Mode
1. Start MongoDB service
2. Run backend: `cd backend && npm run dev`
3. Run frontend: `cd frontend && npm run dev`
4. Open http://localhost:5173 in your browser

### Production Build
```bash
# Build frontend
cd frontend
npm run build

# Start production server
cd ../backend
npm start
```

## 📱 Usage

### Creating Notes
1. Click the "New Note" button (desktop) or the floating + button (mobile)
2. Fill in the title, content, category, and tags
3. Optionally pin the note for priority
4. Click "Create Note" to save

### Managing Notes
- **Edit**: Click on any note card or use the menu (⋮) → Edit
- **Delete**: Use the menu (⋮) → Delete
- **Pin/Unpin**: Use the menu (⋮) → Pin/Unpin
- **Search**: Use the search bar in the header
- **Filter**: Select a category from the dropdown

### Themes
- Click the sun/moon icon in the header to toggle between light and dark themes
- Theme preference is automatically saved

## 🔧 API Endpoints

### Notes
- `GET /api/notes` - Get all notes (with optional filters)
- `GET /api/notes/:id` - Get a specific note
- `POST /api/notes` - Create a new note
- `PUT /api/notes/:id` - Update a note
- `DELETE /api/notes/:id` - Delete a note
- `PATCH /api/notes/:id/pin` - Toggle pin status
- `GET /api/notes/stats` - Get notes statistics

### Health Check
- `GET /api/health` - API health check

## 🔌 Integration Points

This app is designed to be easily integrated into larger web applications:

### As a Component
```jsx
import ThinkBoard from './components/ThinkBoard'

function App() {
  return (
    <div>
      <ThinkBoard 
        apiUrl="your-api-url"
        userId="user-id" // for multi-user support
      />
    </div>
  )
}
```

### As a Microservice
- The backend can be deployed as a standalone microservice
- Frontend can be embedded as an iframe or integrated as a React component
- API can be consumed by other applications

### Database Integration
- Notes collection can be part of a larger MongoDB database
- Easy to add user authentication and multi-tenancy
- Supports additional fields and relationships

## 🚀 Deployment

### Frontend (Vercel/Netlify)
1. Build the project: `npm run build`
2. Deploy the `dist` folder
3. Set environment variables in deployment platform

### Backend (Railway/Heroku/DigitalOcean)
1. Set up MongoDB Atlas or cloud database
2. Configure environment variables
3. Deploy using platform-specific instructions

### Full Stack (Docker)
```dockerfile
# Example Dockerfile for backend
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 5030
CMD ["npm", "start"]
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] Create a new note
- [ ] Edit an existing note
- [ ] Delete a note
- [ ] Pin/unpin notes
- [ ] Search functionality
- [ ] Category filtering
- [ ] Theme switching
- [ ] Mobile responsiveness
- [ ] Offline functionality

### API Testing
Use tools like Postman or curl to test API endpoints:
```bash
# Health check
curl http://localhost:5030/api/health

# Get all notes
curl http://localhost:5030/api/notes

# Create a note
curl -X POST http://localhost:5030/api/notes \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Note","content":"Test content","category":"General"}'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with modern web technologies
- Inspired by popular note-taking applications
- Designed for scalability and maintainability
