/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "m11 19-1.106-.552a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0l4.212 2.106a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619V12",
      key: "svfegj"
    }
  ],
  ["path", { d: "M15 5.764V12", key: "1ocw4k" }],
  ["path", { d: "M18 15v6", key: "9wciyi" }],
  ["path", { d: "M21 18h-6", key: "139f0c" }],
  ["path", { d: "M9 3.236v15", key: "1uimfh" }]
];
const MapPlus = createLucideIcon("map-plus", __iconNode);

export { __iconNode, MapPlus as default };
//# sourceMappingURL=map-plus.js.map
