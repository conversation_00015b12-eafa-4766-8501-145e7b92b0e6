export default {":where(.btn)":{"width":"unset"},".btn":{"display":"inline-flex","flex-shrink":0,"cursor":"pointer","flex-wrap":"nowrap","align-items":"center","justify-content":"center","gap":"calc(0.25rem * 1.5)","text-align":"center","vertical-align":"middle","outline-offset":"2px","webkit-user-select":"none","user-select":"none","padding-inline":"var(--btn-p)","color":"var(--btn-fg)","--tw-prose-links":"var(--btn-fg)","height":"var(--size)","font-size":"var(--fontsize, 0.875rem)","font-weight":600,"outline-color":"var(--btn-color, var(--color-base-content))","transition-property":"color, background-color, border-color, box-shadow","transition-timing-function":"cubic-bezier(0, 0, 0.2, 1)","transition-duration":"0.2s","border-start-start-radius":"var(--join-ss, var(--radius-field))","border-start-end-radius":"var(--join-se, var(--radius-field))","border-end-start-radius":"var(--join-es, var(--radius-field))","border-end-end-radius":"var(--join-ee, var(--radius-field))","background-color":"var(--btn-bg)","background-size":"auto, calc(var(--noise) * 100%)","background-image":"none, var(--btn-noise)","border-width":"var(--border)","border-style":"solid","border-color":"var(--btn-border)","text-shadow":"0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15))","touch-action":"manipulation","box-shadow":"0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow)","--size":"calc(var(--size-field, 0.25rem) * 10)","--btn-bg":"var(--btn-color, var(--color-base-200))","--btn-fg":"var(--color-base-content)","--btn-p":"1rem","--btn-border":"color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%))","--btn-shadow":"0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),\n    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000)","--btn-noise":"var(--fx-noise)",".prose &":{"text-decoration-line":"none"},"@media (hover: hover)":{"&:hover":{"--btn-bg":"color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%)"}},"&:focus-visible":{"outline-width":"2px","outline-style":"solid","isolation":"isolate"},"&:active:not(.btn-active)":{"translate":"0 0.5px","--btn-bg":"color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%)","--btn-border":"color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%)","--btn-shadow":"0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0)"},"&:is(:disabled, [disabled], .btn-disabled)":{"&:not(.btn-link, .btn-ghost)":{"background-color":"color-mix(in oklab, var(--color-base-content) 10%, transparent)","box-shadow":"none"},"pointer-events":"none","--btn-border":"#0000","--btn-noise":"none","--btn-fg":"color-mix(in oklch, var(--color-base-content) 20%, #0000)","@media (hover: hover)":{"&:hover":{"pointer-events":"none","background-color":"color-mix(in oklab, var(--color-neutral) 20%, transparent)","--btn-border":"#0000","--btn-fg":"color-mix(in oklch, var(--color-base-content) 20%, #0000)"}}},"&:is(input[type=\"checkbox\"], input[type=\"radio\"])":{"appearance":"none","&::after":{"content":"attr(aria-label)"}},"&:where(input:checked:not(.filter .btn))":{"--btn-color":"var(--color-primary)","--btn-fg":"var(--color-primary-content)","isolation":"isolate"}},".btn-active":{"--btn-bg":"color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%)","--btn-shadow":"0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0)","isolation":"isolate"},".btn-primary":{"--btn-color":"var(--color-primary)","--btn-fg":"var(--color-primary-content)"},".btn-secondary":{"--btn-color":"var(--color-secondary)","--btn-fg":"var(--color-secondary-content)"},".btn-accent":{"--btn-color":"var(--color-accent)","--btn-fg":"var(--color-accent-content)"},".btn-neutral":{"--btn-color":"var(--color-neutral)","--btn-fg":"var(--color-neutral-content)"},".btn-info":{"--btn-color":"var(--color-info)","--btn-fg":"var(--color-info-content)"},".btn-success":{"--btn-color":"var(--color-success)","--btn-fg":"var(--color-success-content)"},".btn-warning":{"--btn-color":"var(--color-warning)","--btn-fg":"var(--color-warning-content)"},".btn-error":{"--btn-color":"var(--color-error)","--btn-fg":"var(--color-error-content)"},".btn-ghost":{"&:not(.btn-active, :hover, :active:focus, :focus-visible)":{"--btn-shadow":"\"\"","--btn-bg":"#0000","--btn-border":"#0000","--btn-noise":"none","&:not(:disabled, [disabled], .btn-disabled)":{"outline-color":"currentColor","--btn-fg":"currentColor"}},"@media (hover: none)":{"&:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled)":{"--btn-shadow":"\"\"","--btn-bg":"#0000","--btn-border":"#0000","--btn-noise":"none","--btn-fg":"currentColor"}}},".btn-link":{"text-decoration-line":"underline","outline-color":"currentColor","--btn-border":"#0000","--btn-bg":"#0000","--btn-fg":"var(--color-primary)","--btn-noise":"none","--btn-shadow":"\"\"","&:is(.btn-active, :hover, :active:focus, :focus-visible)":{"text-decoration-line":"underline","--btn-border":"#0000","--btn-bg":"#0000"},"@media (hover: none)":{"&:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled)":{"text-decoration-line":"none"}}},".btn-outline":{"&:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked )":{"--btn-shadow":"\"\"","--btn-bg":"#0000","--btn-fg":"var(--btn-color)","--btn-border":"var(--btn-color)","--btn-noise":"none"},"@media (hover: none)":{"&:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked )":{"--btn-shadow":"\"\"","--btn-bg":"#0000","--btn-fg":"var(--btn-color)","--btn-border":"var(--btn-color)","--btn-noise":"none"}}},".btn-dash":{"&:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked )":{"--btn-shadow":"\"\"","border-style":"dashed","--btn-bg":"#0000","--btn-fg":"var(--btn-color)","--btn-border":"var(--btn-color)","--btn-noise":"none"},"@media (hover: none)":{"&:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked )":{"--btn-shadow":"\"\"","border-style":"dashed","--btn-bg":"#0000","--btn-fg":"var(--btn-color)","--btn-border":"var(--btn-color)","--btn-noise":"none"}}},".btn-soft":{"&:not(.btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled)":{"--btn-shadow":"\"\"","--btn-fg":"var(--btn-color, var(--color-base-content))","--btn-bg":"color-mix(\n      in oklab,\n      var(--btn-color, var(--color-base-content)) 8%,\n      var(--color-base-100)\n    )","--btn-border":"color-mix(\n      in oklab,\n      var(--btn-color, var(--color-base-content)) 10%,\n      var(--color-base-100)\n    )","--btn-noise":"none"},"@media (hover: none)":{"&:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled)":{"--btn-shadow":"\"\"","--btn-fg":"var(--btn-color, var(--color-base-content))","--btn-bg":"color-mix(\n        in oklab,\n        var(--btn-color, var(--color-base-content)) 8%,\n        var(--color-base-100)\n      )","--btn-border":"color-mix(\n        in oklab,\n        var(--btn-color, var(--color-base-content)) 10%,\n        var(--color-base-100)\n      )","--btn-noise":"none"}}},".btn-xs":{"--fontsize":"0.6875rem","--btn-p":"0.5rem","--size":"calc(var(--size-field, 0.25rem) * 6)"},".btn-sm":{"--fontsize":"0.75rem","--btn-p":"0.75rem","--size":"calc(var(--size-field, 0.25rem) * 8)"},".btn-md":{"--fontsize":"0.875rem","--btn-p":"1rem","--size":"calc(var(--size-field, 0.25rem) * 10)"},".btn-lg":{"--fontsize":"1.125rem","--btn-p":"1.25rem","--size":"calc(var(--size-field, 0.25rem) * 12)"},".btn-xl":{"--fontsize":"1.375rem","--btn-p":"1.5rem","--size":"calc(var(--size-field, 0.25rem) * 14)"},".btn-square":{"padding-inline":"calc(0.25rem * 0)","width":"var(--size)","height":"var(--size)"},".btn-circle":{"border-radius":"calc(infinity * 1px)","padding-inline":"calc(0.25rem * 0)","width":"var(--size)","height":"var(--size)"},".btn-wide":{"width":"100%","max-width":"calc(0.25rem * 64)"},".btn-block":{"width":"100%"}};