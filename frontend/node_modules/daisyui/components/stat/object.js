export default {".stats":{"position":"relative","display":"inline-grid","grid-auto-flow":"column","overflow-x":"auto","border-radius":"var(--radius-box)"},".stat":{"display":"inline-grid","width":"100%","column-gap":"calc(0.25rem * 4)","padding-inline":"calc(0.25rem * 6)","padding-block":"calc(0.25rem * 4)","grid-template-columns":"repeat(1, 1fr)","&:not(:last-child)":{"border-inline-end":"var(--border) dashed color-mix(in oklab, currentColor 10%, #0000)","border-block-end":"none"}},".stat-figure":{"grid-column-start":"2","grid-row":"span 3 / span 3","grid-row-start":"1","place-self":"center","justify-self":"flex-end"},".stat-title":{"grid-column-start":"1","white-space":"nowrap","color":"color-mix(in oklab, var(--color-base-content) 60%, transparent)","font-size":"0.75rem"},".stat-value":{"grid-column-start":"1","white-space":"nowrap","font-size":"2rem","font-weight":800},".stat-desc":{"grid-column-start":"1","white-space":"nowrap","color":"color-mix(in oklab, var(--color-base-content) 60%, transparent)","font-size":"0.75rem"},".stat-actions":{"grid-column-start":"1","white-space":"nowrap"},".stats-horizontal":{"grid-auto-flow":"column","overflow-x":"auto",".stat:not(:last-child)":{"border-inline-end":"var(--border) dashed color-mix(in oklab, currentColor 10%, #0000)","border-block-end":"none"}},".stats-vertical":{"grid-auto-flow":"row","overflow-y":"auto",".stat:not(:last-child)":{"border-inline-end":"none","border-block-end":"var(--border) dashed color-mix(in oklab, currentColor 10%, #0000)"}}};