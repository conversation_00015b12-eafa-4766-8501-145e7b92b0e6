// import { SearchIcon, SunIcon, MoonIcon } from 'lucide-react'
const SearchIcon = ({ size }) => <span style={{ fontSize: size }}>🔍</span>
const SunIcon = ({ size }) => <span style={{ fontSize: size }}>☀️</span>
const MoonIcon = ({ size }) => <span style={{ fontSize: size }}>🌙</span>
import { useState, useEffect } from 'react'

const Header = ({ searchTerm, setSearchTerm, selectedCategory, setSelectedCategory, notes }) => {
  const [theme, setTheme] = useState('light')

  // Get unique categories from notes
  const categories = ['all', ...new Set(notes.map(note => note.category))]

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') || 'light'
    setTheme(savedTheme)
    document.documentElement.setAttribute('data-theme', savedTheme)
  }, [])

  // Toggle theme
  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
    localStorage.setItem('theme', newTheme)
    document.documentElement.setAttribute('data-theme', newTheme)
  }

  return (
    <header className="bg-base-100 shadow-sm border-b border-base-300">
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Logo and Title */}
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-content font-bold text-xl">T</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-base-content">ThinkBoard</h1>
              <p className="text-sm text-base-content/60">Your digital notebook</p>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-3 lg:flex-1 lg:max-w-2xl lg:mx-8">
            {/* Search Input */}
            <div className="relative flex-1">
              <SearchIcon 
                size={20} 
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/50" 
              />
              <input
                type="text"
                placeholder="Search notes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input input-bordered w-full pl-10 focus:input-primary"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="select select-bordered focus:select-primary min-w-32"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          {/* Theme Toggle */}
          <div className="flex items-center gap-2">
            <button
              onClick={toggleTheme}
              className="btn btn-ghost btn-circle"
              title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
            >
              {theme === 'light' ? (
                <MoonIcon size={20} />
              ) : (
                <SunIcon size={20} />
              )}
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
