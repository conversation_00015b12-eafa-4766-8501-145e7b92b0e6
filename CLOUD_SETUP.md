# Cloud Setup Guide - ThinkBoard

This guide will help you set up cloud services for your ThinkBoard application instead of using local storage, which is recommended for production deployment and to avoid overloading local compute power.

## 🌐 Cloud Services Overview

We'll use these cloud services:
- **MongoDB Atlas** - Cloud MongoDB database (Free tier available)
- **Upstash Redis** - Cloud Redis for rate limiting (Free tier available)
- **Vercel/Netlify** - Frontend hosting (Free tier available)
- **Railway/Heroku** - Backend hosting (Free/affordable tiers available)

## 📊 MongoDB Atlas Setup

### Step 1: Create MongoDB Atlas Account
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Click "Try Free" and create an account
3. Choose "Build a database" → "M0 Sandbox" (Free tier)
4. Select your preferred cloud provider and region
5. Create cluster (takes 1-3 minutes)

### Step 2: Configure Database Access
1. In Atlas dashboard, go to "Database Access"
2. Click "Add New Database User"
3. Choose "Password" authentication
4. Create username and strong password
5. Set "Built-in Role" to "Read and write to any database"
6. Click "Add User"

### Step 3: Configure Network Access
1. Go to "Network Access" in Atlas dashboard
2. Click "Add IP Address"
3. For development: Click "Allow Access from Anywhere" (0.0.0.0/0)
4. For production: Add specific IP addresses of your servers
5. Click "Confirm"

### Step 4: Get Connection String
1. Go to "Database" → "Connect" → "Connect your application"
2. Select "Node.js" and version "4.1 or later"
3. Copy the connection string
4. Replace `<password>` with your database user password
5. Replace `<dbname>` with `thinkboard` (or your preferred name)

Example connection string:
```
mongodb+srv://username:<EMAIL>/thinkboard?retryWrites=true&w=majority
```

## 🚀 Upstash Redis Setup

### Step 1: Create Upstash Account
1. Go to [Upstash Console](https://console.upstash.com/)
2. Sign up with GitHub, Google, or email
3. Verify your email if required

### Step 2: Create Redis Database
1. Click "Create Database"
2. Choose a name (e.g., "thinkboard-redis")
3. Select region closest to your backend deployment
4. Choose "Free" tier (up to 10,000 commands/day)
5. Click "Create"

### Step 3: Get Redis Credentials
1. In your Redis database dashboard
2. Copy the "UPSTASH_REDIS_REST_URL"
3. Copy the "UPSTASH_REDIS_REST_TOKEN"
4. These will be used in your environment variables

## 🔧 Environment Configuration

### Backend Environment Variables (.env)
```env
# MongoDB Atlas
MONGO_URI=mongodb+srv://username:<EMAIL>/thinkboard?retryWrites=true&w=majority

# Upstash Redis
UPSTASH_REDIS_REST_URL=https://your-endpoint.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-token-here

# Server Configuration
NODE_ENV=production
PORT=5030

# CORS Configuration
FRONTEND_URL=https://your-frontend-domain.com
```

### Frontend Environment Variables (.env)
```env
# API Configuration
VITE_API_URL=https://your-backend-domain.com/api

# App Configuration
VITE_APP_NAME=ThinkBoard
VITE_APP_VERSION=1.0.0
```

## 🚀 Deployment Platforms

### Option 1: Vercel (Frontend) + Railway (Backend)

#### Frontend on Vercel
1. Push code to GitHub
2. Connect repository to Vercel
3. Set build settings:
   - Framework: Vite
   - Build Command: `cd frontend && npm run build`
   - Output Directory: `frontend/dist`
4. Add environment variables in Vercel dashboard

#### Backend on Railway
1. Connect GitHub repository to Railway
2. Set root directory to `backend`
3. Railway will auto-detect Node.js
4. Add environment variables in Railway dashboard
5. Deploy automatically on git push

### Option 2: Netlify (Frontend) + Heroku (Backend)

#### Frontend on Netlify
1. Connect GitHub repository
2. Set build settings:
   - Build command: `cd frontend && npm run build`
   - Publish directory: `frontend/dist`
3. Add environment variables in Netlify dashboard

#### Backend on Heroku
1. Create new Heroku app
2. Connect GitHub repository
3. Set buildpack to Node.js
4. Add environment variables in Heroku dashboard
5. Enable automatic deploys

## 🔒 Security Best Practices

### MongoDB Atlas Security
- ✅ Use strong passwords for database users
- ✅ Restrict IP access to known servers only (production)
- ✅ Enable MongoDB's built-in security features
- ✅ Regularly rotate database passwords
- ✅ Use separate databases for development/production

### Redis Security
- ✅ Keep Redis tokens secure and private
- ✅ Use different Redis instances for dev/prod
- ✅ Monitor Redis usage and set up alerts
- ✅ Regularly rotate access tokens

### General Security
- ✅ Use HTTPS for all communications
- ✅ Set secure CORS origins
- ✅ Use environment variables for all secrets
- ✅ Enable rate limiting in production
- ✅ Regular security updates

## 💰 Cost Optimization

### Free Tier Limits
- **MongoDB Atlas M0**: 512 MB storage, shared RAM/CPU
- **Upstash Redis**: 10,000 commands/day, 256 MB storage
- **Vercel**: 100 GB bandwidth/month, unlimited static sites
- **Railway**: $5 credit/month, pay-as-you-go after

### Scaling Considerations
- Monitor usage through cloud dashboards
- Set up billing alerts
- Upgrade tiers as needed
- Consider reserved instances for predictable workloads

## 🔧 Testing Cloud Setup

### 1. Test Database Connection
```bash
# In your backend directory
node -e "
const mongoose = require('mongoose');
mongoose.connect(process.env.MONGO_URI)
  .then(() => console.log('✅ MongoDB Atlas connected!'))
  .catch(err => console.error('❌ Connection failed:', err));
"
```

### 2. Test Redis Connection
```bash
# Test Redis connection
curl -X POST https://your-redis-endpoint.upstash.io/ping \
  -H "Authorization: Bearer your-redis-token"
```

### 3. Test API Endpoints
```bash
# Test health endpoint
curl https://your-backend-domain.com/api/health

# Test notes endpoint
curl https://your-backend-domain.com/api/notes
```

## 🚨 Troubleshooting

### Common MongoDB Issues
- **Connection timeout**: Check network access whitelist
- **Authentication failed**: Verify username/password
- **Database not found**: Ensure database name in connection string

### Common Redis Issues
- **Unauthorized**: Check Redis token
- **Rate limit exceeded**: Monitor usage in Upstash dashboard
- **Connection failed**: Verify Redis URL format

### Deployment Issues
- **Build failures**: Check environment variables
- **CORS errors**: Verify frontend URL in backend CORS config
- **API not found**: Check API base URL in frontend

## 📞 Support Resources

### MongoDB Atlas
- [Documentation](https://docs.atlas.mongodb.com/)
- [Community Forums](https://community.mongodb.com/)
- [Support Portal](https://support.mongodb.com/)

### Upstash
- [Documentation](https://docs.upstash.com/)
- [Discord Community](https://discord.gg/w9SenAtbme)
- [Support Email](mailto:<EMAIL>)

### Deployment Platforms
- [Vercel Docs](https://vercel.com/docs)
- [Railway Docs](https://docs.railway.app/)
- [Netlify Docs](https://docs.netlify.com/)
- [Heroku Docs](https://devcenter.heroku.com/)

## ✅ Cloud Setup Checklist

- [ ] MongoDB Atlas cluster created
- [ ] Database user configured with proper permissions
- [ ] Network access configured (IP whitelist)
- [ ] Connection string obtained and tested
- [ ] Upstash Redis database created
- [ ] Redis credentials obtained and tested
- [ ] Backend environment variables configured
- [ ] Frontend environment variables configured
- [ ] Deployment platform chosen and configured
- [ ] CORS settings updated for production domains
- [ ] Security best practices implemented
- [ ] Monitoring and alerts set up
- [ ] Backup strategy planned

Once you complete this checklist, your ThinkBoard application will be running entirely on cloud infrastructure, providing better performance, reliability, and scalability!